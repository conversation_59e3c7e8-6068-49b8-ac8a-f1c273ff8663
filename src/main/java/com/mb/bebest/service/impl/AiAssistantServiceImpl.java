package com.mb.bebest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mb.bebest.client.AiApiClient;
import com.mb.bebest.common.BusinessException;
import com.mb.bebest.common.ExceptionEnum;
import com.mb.bebest.constant.Constants;
import com.mb.bebest.util.UserContextUtil;
import com.mb.bebest.dto.request.AiAssistantSearchRequest;
import com.mb.bebest.dto.response.AiAssistantSearchResponse;
import com.mb.bebest.dto.response.AiSearchResponse;
import com.mb.bebest.dto.response.SearchHistoryResponse;
import com.mb.bebest.entity.AiSummaryIssue;
import com.mb.bebest.entity.SearchHistory;
import com.mb.bebest.entity.SearchHistoryIssue;
import com.mb.bebest.repository.AiSummaryIssueRepository;
import com.mb.bebest.repository.SearchHistoryRepository;
import com.mb.bebest.repository.SearchHistoryIssueRepository;
import com.mb.bebest.service.AiAssistantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.Predicate;

import java.math.BigDecimal;
import java.time.LocalDate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * AI助手服务实现类
 * 实现AI助手相关的业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiAssistantServiceImpl implements AiAssistantService {

    private final AiSummaryIssueRepository aiSummaryIssueRepository;
    private final SearchHistoryRepository searchHistoryRepository;
    private final SearchHistoryIssueRepository searchHistoryIssueRepository;
    private final AiApiClient aiApiClient;

    /**
     * 调用AI API并保存数据
     * @param query 查询内容
     * @return AI搜索结果响应
     */
    @Override
    @Transactional
    public AiAssistantSearchResponse callAiAndSaveData(String query) {
        // 获取当前用户ID
        Long currentUserId = UserContextUtil.getCurrentUserId();
        log.info("Call AI API and save data, userId: {}, query: {}", currentUserId, query);

        // 1. 调用AI API进行查询
        List<AiSearchResponse.AiIssueResult> aiResults = callAiApi(query);

        // 2. 保存搜索数据并构建响应
        return saveSearchDataAndBuildResponse(currentUserId, query, aiResults);
    }

    /**
     * 基于数据库搜索相似问题
     * @param request 搜索请求参数
     * @return 搜索结果响应
     */
    @Override
    @Transactional(readOnly = true)
    public AiAssistantSearchResponse searchSimilarIssues(AiAssistantSearchRequest request) {
        // 获取当前用户ID
        Long currentUserId = UserContextUtil.getCurrentUserId();
        log.info("Search similar issues from database, userId: {}, searchHistoryId: {}", currentUserId, request.getId());

        // 验证搜索历史是否存在且属于当前用户
        validateSearchHistory(currentUserId, request.getId());

        // 基于数据库查询相似问题
        List<AiSummaryIssue> issues = searchIssuesFromDatabase(request);

        // 构建响应结果
        return buildResponse(issues, (long) issues.size());
    }

    /**
     * 验证搜索历史是否存在且属于当前用户
     */
    private void validateSearchHistory(Long userId, Long searchHistoryId) {
        SearchHistory searchHistory = searchHistoryRepository.findById(searchHistoryId)
                .orElseThrow(() -> new BusinessException(ExceptionEnum.SEARCH_HISTORY_NOT_FOUND));

        // 验证权限：只能访问自己的搜索历史
        if (!userId.equals(searchHistory.getUserId())) {
            throw new BusinessException(ExceptionEnum.ACCESS_DENIED);
        }
    }

    /**
     * 基于数据库搜索相似问题（查询指定搜索历史关联的问题）
     */
    private List<AiSummaryIssue> searchIssuesFromDatabase(AiAssistantSearchRequest request) {
        // 先获取该搜索历史关联的所有问题ID
        List<SearchHistoryIssue> historyIssues = searchHistoryIssueRepository.findBySearchHistoryId(request.getId());

        if (CollUtil.isEmpty(historyIssues)) {
            return new ArrayList<>();
        }

        // 提取问题ID列表
        List<Long> issueIds = historyIssues.stream()
                .map(SearchHistoryIssue::getAiSummaryIssueId)
                .collect(Collectors.toList());

        // 创建排序条件：按相似度分数降序，然后按创建时间降序
        Sort sort = Sort.by(Sort.Order.desc("similarityScore"), Sort.Order.desc("createdAt"));

        // 使用Specification进行条件查询（限定在关联的问题范围内）
        return aiSummaryIssueRepository.findAll(specifications(request, issueIds), sort);
    }

    /**
     * 构建AiSummaryIssue查询条件（限定在指定问题ID范围内）
     */
    private Specification<AiSummaryIssue> specifications(AiAssistantSearchRequest request, List<Long> issueIds) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 限定查询范围：只查询关联的问题ID
            predicates.add(root.get("id").in(issueIds));

            // 平台精确查询
            if (StrUtil.isNotBlank(request.getPlatform())) {
                predicates.add(criteriaBuilder.equal(root.get("platform"), request.getPlatform()));
            }

            // 车型精确查询
            if (StrUtil.isNotBlank(request.getCarline())) {
                predicates.add(criteriaBuilder.equal(root.get("carline"), request.getCarline()));
            }

            // 状态精确查询
            if (StrUtil.isNotBlank(request.getStatus())) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }

            // 数据源集合查询
            if (CollUtil.isNotEmpty(request.getDataSources())) {
                predicates.add(root.get("dataSource").in(request.getDataSources()));
            }

            // 问题日期区间查询（开始日期和结束日期都不为空才进行查询）
            if (ObjectUtil.isNotEmpty(request.getStartDate()) && ObjectUtil.isNotEmpty(request.getEndDate())) {
                predicates.add(criteriaBuilder.between(root.get("issueDate"), request.getStartDate(), request.getEndDate()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }



    /**
     * 调用AI API进行分析
     */
    private List<AiSearchResponse.AiIssueResult> callAiApi(String query) {
        log.info("Calling AI API, query: {}", query);

        try {
            // 调用真实的AI API
            // return aiApiClient.analyzeIssue(query);

            // Mock数据用于本地测试
            return createMockAiResults(query);
        } catch (Exception e) {
            log.error("AI API call failed, query: {}", query, e);
            throw new BusinessException(ExceptionEnum.AI_API_CALL_FAILED);
        }
    }





    /**
     * 创建Mock AI分析结果（用于本地测试）
     */
    private List<AiSearchResponse.AiIssueResult> createMockAiResults(String query) {
        List<AiSearchResponse.AiIssueResult> mockResults = new ArrayList<>();

        // Mock数据1
        mockResults.add(AiSearchResponse.AiIssueResult.builder()
                .issueId("MOCK-" + UUID.randomUUID().toString())
                .title(query)
                .descriptionSummary("客户反映发动机在怠速时出现异响，经检查发现是正时链条松动导致")
                .dataSource("MockSource1")
                .platform("254")
                .carline("V294")
                .status("Open")
                .issueDate(LocalDate.now().minusDays(5))
                .similarityScore(new BigDecimal("0.95"))
                .sourceIssueId("SRC-001")
                .build());

        // Mock数据2
        mockResults.add(AiSearchResponse.AiIssueResult.builder()
                .issueId("MOCK-" + UUID.randomUUID().toString())
                .title(query)
                .descriptionSummary("自动变速箱在2-3档换挡时出现明显顿挫感，影响驾驶体验")
                .dataSource("MockSource2")
                .platform("254")
                .carline("V294")
                .status("In Progress")
                .issueDate(LocalDate.now().minusDays(3))
                .similarityScore(new BigDecimal("0.88"))
                .sourceIssueId("SRC-002")
                .build());

        // Mock数据3
        mockResults.add(AiSearchResponse.AiIssueResult.builder()
                .issueId("MOCK-" + UUID.randomUUID().toString())
                .title(query)
                .descriptionSummary("夏季高温时空调制冷效果明显下降，压缩机工作正常但制冷剂不足")
                .dataSource("MockSource3")
                .platform("254")
                .carline("V294")
                .status("Resolved")
                .issueDate(LocalDate.now().minusDays(1))
                .similarityScore(new BigDecimal("0.76"))
                .sourceIssueId("SRC-003")
                .build());

        log.info("Created {} mock AI results for query: {}", mockResults.size(), query);
        return mockResults;
    }

    /**
     * 保存搜索数据并构建响应（用于callAiAndSaveData）
     */
    private AiAssistantSearchResponse saveSearchDataAndBuildResponse(Long userId, String query, List<AiSearchResponse.AiIssueResult> aiResults) {
        // 保存搜索历史
        SearchHistory searchHistory = saveSearchHistory(userId, query, (long) aiResults.size());

        // 保存AI问题数据
        List<AiSummaryIssue> savedIssues = saveAiSummaryIssues(aiResults);

        // 保存关联关系
        saveSearchHistoryIssues(searchHistory.getId(), savedIssues);

        // 构建响应结果
        return buildResponse(savedIssues, (long) aiResults.size());
    }



    /**
     * 保存搜索历史（用于callAiAndSaveData）
     */
    private SearchHistory saveSearchHistory(Long userId, String query, Long resultCount) {
        // 构建搜索参数Map
        Map<String, Object> searchParams = new HashMap<>();
        searchParams.put("query", query);
        searchParams.put("dataSources", null);


        SearchHistory searchHistory = SearchHistory.builder()
                .userId(userId)
                .query(query)
                .searchParams(JSONUtil.toJsonStr(searchParams))
                .resultCount(resultCount.intValue())
                .build();

        return searchHistoryRepository.save(searchHistory);
    }

    /**
     * 保存AI问题数据
     */
    private List<AiSummaryIssue> saveAiSummaryIssues(List<AiSearchResponse.AiIssueResult> aiResults) {
        if (CollUtil.isEmpty(aiResults)) {
            return new ArrayList<>();
        }

        List<AiSummaryIssue> issues = aiResults.stream()
                .map(this::convertToAiSummaryIssue)
                .collect(Collectors.toList());

        return aiSummaryIssueRepository.saveAll(issues);
    }

    /**
     * 转换AI结果为AiSummaryIssue实体
     */
    private AiSummaryIssue convertToAiSummaryIssue(AiSearchResponse.AiIssueResult aiResult) {
        return AiSummaryIssue.builder()
                .issueId(aiResult.getIssueId())
                .title(aiResult.getTitle())
                .descriptionSummary(aiResult.getDescriptionSummary())
                .customerComplaint(aiResult.getCustomerComplaint())
                .dataSource(aiResult.getDataSource())
                .platform(aiResult.getPlatform())
                .carline(aiResult.getCarline())
                .status(aiResult.getStatus())
                .issueType(Constants.DATA_TYPE_REAL_TIME_RETRIEVAL)
                .moduleGroup(aiResult.getModuleGroup())
                .project(aiResult.getProject())
                .damageLocation(aiResult.getDamageLocation())
                .damageType(aiResult.getDamageType())
                .damageCode(aiResult.getDamageCode())
                .responsiblePerson(aiResult.getResponsiblePerson())
                .issueDate(aiResult.getIssueDate())
                .similarityScore(aiResult.getSimilarityScore())
                .rankOrder(aiResult.getRankOrder())
                .sourceIssueId(aiResult.getSourceIssueId())
                .build();
    }

    /**
     * 保存搜索历史与问题的关联关系
     */
    private void saveSearchHistoryIssues(Long searchHistoryId, List<AiSummaryIssue> issues) {
        if (CollUtil.isEmpty(issues)) {
            return;
        }

        List<SearchHistoryIssue> relations = issues.stream()
                .map(issue -> SearchHistoryIssue.builder()
                        .searchHistoryId(searchHistoryId)
                        .aiSummaryIssueId(issue.getId())
                        .build())
                .collect(Collectors.toList());

        searchHistoryIssueRepository.saveAll(relations);
    }

    /**
     * 构建响应结果
     */
    private AiAssistantSearchResponse buildResponse(List<AiSummaryIssue> issues, Long total) {
        List<AiAssistantSearchResponse.SearchResult> results = issues.stream()
                .map(this::convertToSearchResult)
                .collect(Collectors.toList());

        return AiAssistantSearchResponse.builder()
                .total(total)
                .results(results)
                .build();
    }

    /**
     * 转换AiSummaryIssue为SearchResult
     */
    private AiAssistantSearchResponse.SearchResult convertToSearchResult(AiSummaryIssue issue) {
        return AiAssistantSearchResponse.SearchResult.builder()
                .issueId(issue.getIssueId())
                .title(issue.getTitle())
                .descriptionSummary(issue.getDescriptionSummary())
                .source(issue.getDataSource())
                .date(issue.getIssueDate())
                .carline(issue.getCarline())
                .platform(issue.getPlatform())
                .status(issue.getStatus())
                .similarity(ObjectUtil.isNotEmpty(issue.getSimilarityScore()) ? issue.getSimilarityScore().toString() : null)
                .sourceIssueId(issue.getSourceIssueId())
                .build();
    }

    /**
     * 获取搜索历史（Map版本：一次查询，直接返回Map）
     */
    @Override
    @Transactional(readOnly = true)
    public Map<String, List<SearchHistoryResponse.SearchHistoryItem>> getSearchHistory() {
        // 在Service层获取当前用户ID
        Long userId = UserContextUtil.getCurrentUserId();
        log.info("Get search history, userId: {}", userId);

        LocalDateTime now = LocalDateTime.now();

        // 计算15天前的时间作为查询起始时间
        LocalDateTime fifteenDaysAgo = now.toLocalDate().minusDays(Constants.DAYS_FIFTEEN_LIMIT).atStartOfDay();

        // 一次性查询15天内的所有搜索历史并直接分组处理
        Map<String, List<SearchHistoryResponse.SearchHistoryItem>> groupedHistory = searchHistoryRepository
                .findByUserIdAndSearchTimeBetweenOrderBySearchTimeDesc(userId, fifteenDaysAgo, now)
                .stream()
                .collect(Collectors.groupingBy(
                        history -> classifyTimeRange(history.getSearchTime().toLocalDate(), now.toLocalDate()),
                        Collectors.mapping(this::convertToSearchHistoryItem, Collectors.toList())
                ));

        // 按照指定顺序重新组织Map
        return Stream.of(Constants.TODAY, Constants.YESTERDAY, Constants.SEVEN_DAYS, Constants.FIFTEEN_DAYS)
                .filter(groupedHistory::containsKey)
                .collect(Collectors.toMap(
                        key -> key,
                        groupedHistory::get,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
    }

    /**
     * 删除搜索历史
     */
    @Override
    @Transactional
    public void deleteSearchHistory(Long historyId) {
        // 获取当前用户ID
        Long currentUserId = UserContextUtil.getCurrentUserId();
        log.info("Delete search history, userId: {}, historyId: {}", currentUserId, historyId);

        // 查询搜索历史记录
        SearchHistory searchHistory = searchHistoryRepository.findById(historyId)
                .orElseThrow(() -> new BusinessException(ExceptionEnum.SEARCH_HISTORY_NOT_FOUND));

        // 验证权限：只能删除自己的搜索历史
        if (!currentUserId.equals(searchHistory.getUserId())) {
            throw new BusinessException(ExceptionEnum.ACCESS_DENIED);
        }

        // 先删除关联表数据（外键约束）
        searchHistoryIssueRepository.deleteBySearchHistoryId(historyId);

        // 再删除搜索历史主表记录
        searchHistoryRepository.deleteById(historyId);
        log.info("Search history deleted successfully, historyId: {}", historyId);
    }





    /**
     * 转换SearchHistory为SearchHistoryItem
     */
    private SearchHistoryResponse.SearchHistoryItem convertToSearchHistoryItem(SearchHistory history) {
        return SearchHistoryResponse.SearchHistoryItem.builder()
                .id(history.getId())
                .query(history.getQuery())
                .searchTime(history.getSearchTime())
                .resultCount(history.getResultCount())
                .build();
    }

    /**
     * 根据历史记录日期分类时间范围
     * @param historyDate 历史记录日期
     * @param today 今天日期
     * @return 时间范围标识
     */
    private String classifyTimeRange(LocalDate historyDate, LocalDate today) {
        long days = today.toEpochDay() - historyDate.toEpochDay();

        return switch ((int) days) {
            case Constants.DAYS_TODAY -> Constants.TODAY;
            case Constants.DAYS_YESTERDAY -> Constants.YESTERDAY;
            default -> days <= Constants.DAYS_SEVEN_LIMIT ?
                      Constants.SEVEN_DAYS : Constants.FIFTEEN_DAYS;
        };
    }


}
