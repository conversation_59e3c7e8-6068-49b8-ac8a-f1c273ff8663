package com.mb.bebest.config;


import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Configuration
@RequiredArgsConstructor
public class SpringDocConfig {
    @Value("${springdoc.domain-url}")
    private String domainUrl;

    @Value("${springdoc.title}")
    private String title;

    @Value("${springdoc.description}")
    private String description;

    @Value("${springdoc.version}")
    private String version;

    @Bean
    public OpenAPI restfulOpenAPI() {
        List<Server> servers = new ArrayList<>();
        servers.add(new Server().url(domainUrl));
        return new OpenAPI()
                .info(new Info().title(title)
                        .description(description)
                        .version(version))
                .servers(servers)
                .addSecurityItem(new SecurityRequirement()
                        .addList(AUTHORIZATION))
                .components(new Components().addSecuritySchemes(AUTHORIZATION, new SecurityScheme().description("Bearer Token")
                        .name(AUTHORIZATION).type(SecurityScheme.Type.APIKEY).in(SecurityScheme.In.HEADER)));
    }
}